<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('intern_grades', function (Blueprint $table) {
            $table->string('status')->after('final_grade')->nullable();
        });
    }

    public function down()
    {
        Schema::table('intern_grades', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
}; 