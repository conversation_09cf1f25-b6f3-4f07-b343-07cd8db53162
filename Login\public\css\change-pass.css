/* ==== Base Styles ==== */
body {
    margin: 20px;
    margin-top: 3%;
    font-family: Poppins, sans-serif;
}

h2 {
    margin-left: 50px;
    font-weight: 500;
}

p {
    text-align: center;
    font-size: 20px;
}

/* ==== Container ==== */
.change-password-container {
    width: 500px;
    height: auto; /* Let the height adjust naturally */
    margin: 0 auto; /* Center the form horizontally */
    padding: 30px;
    border: 2px solid #22bbea;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* ==== Inside Container ==== */
.change-password-container img {
    width: 250px;
    margin-bottom: 30px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.change-password-container label {
    align-self: flex-start;
    font-size: 16px;
    margin-top: 10px;
}

/* Input fields */
.change-password-container input[type="password"] {
    width: 100%;
    padding: 15px;
    margin-top: 5px;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
    box-sizing: border-box;
}

/* Button */
.change-password-container button {
    width: 100%;
    padding: 15px;
    margin-top: 10px;
    border: none;
    border-radius: 5px;
    background-color: #007bff;
    color: white;
    font-size: 16px;
    cursor: pointer;
    box-sizing: border-box;
}

.change-password-container button:hover {
    background-color: #0056b3;
}

/* Error Message */
.error-message {
    display: block;
    margin-top: 10px;
    font-size: 12px;
    color: red;
    width: 100%;
    text-align: left;
}
