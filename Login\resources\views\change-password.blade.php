<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ asset('css/change-pass.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <title>Change Password</title>
</head>
<body>

<h2>Change Password</h2>
    <div class="change-password-container">


        <form action="{{ route('update-password') }}" method="POST">
        <img src="{{ asset('images/pnlogo.png') }}" alt="Logo">
            @csrf
            <input type="hidden" name="token" value="{{ $token }}">
            <div class="form-group">
                <label for="current_password">Current Password</label>
                <input type="password" name="current_password" id="current_password" class="form-control">
                @error('current_password')
                    <div class="alert alert-danger" style="color: red">
                        <i data-feather="alert-circle" class="w-4 h-4 mr-1"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>
            <div class="form-group">
                <label for="new_password">New Password</label>
                <input type="password" name="new_password" id="new_password" class="form-control">
                @error('new_password')
                    <div class="alert alert-danger" style="color: red">
                        <i data-feather="alert-circle" class="w-4 h-4 mr-1"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>
            <div class="form-group">
                <label for="new_password_confirmation">Confirm New Password</label>
                <input type="password" name="new_password_confirmation" id="new_password_confirmation" class="form-control">
                @error('new_password_confirmation')
                    <div class="alert alert-danger" style="color: red">
                        <i data-feather="alert-circle" class="w-4 h-4 mr-1"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>
            <button type="submit" class="btn btn-primary">Update Password</button>
        </form>
</div>



</body>
</html>



