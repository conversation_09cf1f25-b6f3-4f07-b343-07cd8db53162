<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PNUser;
use App\Models\StudentDetail;
use Illuminate\Support\Facades\Auth;
use App\Models\Going_out;
use App\Models\Schedule;
use App\Models\NotificationView;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class GoingOutLogController extends Controller
{


    /**
     * Handle going-out log creation for the current user.
     */

    public function logoutForm()
    {
        Log::info('Accessed going out logout form.', [
            'user_id' => Auth::id(),
        ]);
        $loginLog = false;
        $logoutLog = true;
        return view('user-student.goingOutLog', ['loginLog' => $loginLog, 'logoutLog' => $logoutLog]);
    }

    public function loginForm()
    {
        Log::info('Accessed going out login form.', [
            'user_id' => Auth::id(),
        ]);
        $loginLog = true;
        $logoutLog = false;
        return view('user-student.goingOutLog', ['loginLog' => $loginLog, 'logoutLog' => $logoutLog]);
    }

    /**
     * Store a new going-out log (logout).
     */
    public function logTimeOut(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'student_id' => 'required|exists:student_details,student_id',
            'destination' => 'required|string',
            'purpose' => 'required|string',
        ]);

        $student = StudentDetail::where('student_id', $request->student_id)->first();

        if (!$student) {
            Log::warning('Student not found for going out logTimeOut.', [
                'student_id' => $request->student_id,
            ]);
            return redirect()->route('goingOutLogForms.show')->with('error', 'Student not found.');
        }

        $today = now()->format('l');

        // Clear any potential model cache and force fresh query
        Schedule::clearBootedModels();

        // First check for individual going-out schedule for this specific student
        $schedule = Schedule::where([
                ['student_id', $student->student_id],
                ['day_of_week', $today],
                ['schedule_type', 'going_out'] // Only look for going-out schedules
            ])->where(function ($query) {
                $query->whereNull('valid_until') // Permanent schedule
                    ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
            })
            ->orderBy('updated_at', 'desc') // Most recently updated first
            ->orderBy('created_at', 'desc')  // Then most recently created
            ->first();

        // If no individual schedule found, fall back to general going-out schedule
        if (!$schedule) {
            $schedule = Schedule::where([
                    ['gender', $student->user->gender],
                    ['day_of_week', $today],
                    ['student_id', null], // General schedules have null student_id
                    ['schedule_type', 'going_out'] // Only look for going-out schedules
                ])->where(function ($query) {
                    $query->whereNull('valid_until') // Permanent schedule
                        ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
                })
                ->orderBy('updated_at', 'desc') // Most recently updated first
                ->orderBy('created_at', 'desc')  // Then most recently created
                ->first();
        }

        if (!$schedule) {
            Log::warning('Student attempted going out logout with no schedule', [
                'student_id' => $student->student_id,
                'gender' => $student->user->gender,
                'day' => $today,
            ]);
            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'No schedule set for today! Please contact your educator for assistance.');
        }

        // CORRECTED LOGIC: time_out is the START time, time_in is the END time
        $currentTime = Carbon::now();
        $currentDate = $currentTime->toDateString();

        // Parse schedule times with today's date for proper comparison
        $scheduleStartTime = Carbon::parse($currentDate . ' ' . $schedule->getRawTimeOut());  // When students can start going out
        $scheduleEndTime = Carbon::parse($currentDate . ' ' . $schedule->getRawTimeIn());  // When students must return

        // Check if it's too early to logout (before scheduled start time)
        if ($currentTime->lt($scheduleStartTime)) {
            Log::warning('Student attempted going out logout before schedule started', [
                'student_id' => $student->student_id,
                'current_time' => $currentTime->format('H:i:s'),
                'schedule_start_time' => $scheduleStartTime->format('H:i:s'),
                'raw_schedule_time_out' => $schedule->getRawTimeOut(),
                'raw_schedule_time_in' => $schedule->getRawTimeIn(),
                'current_full_time' => $currentTime->format('Y-m-d H:i:s'),
                'schedule_start_full_time' => $scheduleStartTime->format('Y-m-d H:i:s')
            ]);

            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'Going out period has not started yet! Going out starts at ' . $scheduleStartTime->format('g:i A') . '.');
        }

        // Check if it's too late to logout (after scheduled end time)
        if ($currentTime->gt($scheduleEndTime)) {
            Log::warning('Student attempted going out logout after schedule ended', [
                'student_id' => $student->student_id,
                'current_time' => $currentTime->format('H:i:s'),
                'schedule_end_time' => $scheduleEndTime->format('H:i:s'),
                'raw_schedule_time_out' => $schedule->getRawTimeOut(),
                'raw_schedule_time_in' => $schedule->getRawTimeIn(),
                'current_full_time' => $currentTime->format('Y-m-d H:i:s'),
                'schedule_end_full_time' => $scheduleEndTime->format('Y-m-d H:i:s')
            ]);

            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'Going out period has ended! Going out ended at ' . $scheduleEndTime->format('g:i A') . '.');
        }

        // Check day of week for session logic
        $isSunday = now()->isSunday();
        $todayDate = now()->toDateString();

        if ($isSunday) {
            // Sunday: Single session logic (existing behavior)
            $goingOut = Going_out::where('student_id', $request->student_id)
                ->whereDate('going_out_date', $todayDate)
                ->first();

            if (!$goingOut) {
                Log::warning('Going out log not found for time out.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'The Logs does not reset yet. Wait until 12:00 AM.');
            }

            // Check if monitor already logged out this student (priority check)
            if ($goingOut->monitor_logged_out) {
                return redirect()->route('goingOutLogForms.show')->with('error', 'Monitor has already logged you out. You cannot log out again.');
            }

            if ($goingOut->time_out) {
                Log::info('Time out already logged for going out.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'You can only go out once on Sunday.');
            }
        } else {
            // Monday-Saturday: Multiple sessions allowed, but must complete previous session first
            if (!Going_out::canStartNewSession($request->student_id, $todayDate)) {
                $activeSession = Going_out::getCurrentActiveSession($request->student_id, $todayDate);
                if ($activeSession && !$activeSession->time_in) {
                    return redirect()->route('goingOutLogForms.show')->with('error', 'You must log in from your current going out session before starting a new one.');
                }
            }
        }

        // Calculate remark based on schedule with fixed timing (no grace period for going out)
        $createdBy = $student->user->user_fname . ' ' . $student->user->user_lname;

        // Since we reach here, student is logging out during valid schedule period
        // Always mark as "On Time" since students can only log out during scheduled time
        $remark = 'On Time';

        if ($isSunday) {
            // Sunday: Use existing updateOrCreate logic
            Going_out::updateOrCreate(
                [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ],
                [
                    'session_number' => 1,
                    'session_status' => 'active',
                    'destination' => $request->destination,
                    'purpose' => $request->purpose,
                    'time_out' => now(),
                    'time_out_remark' => $remark,
                    'created_by' => $createdBy,
                    'created_at' => now()
                ]
            );
        } else {
            // Monday-Saturday: Create new session
            $sessionNumber = Going_out::getNextSessionNumber($request->student_id, $todayDate);

            Going_out::create([
                'student_id' => $request->student_id,
                'going_out_date' => $todayDate,
                'session_number' => $sessionNumber,
                'session_status' => 'active',
                'destination' => $request->destination,
                'purpose' => $request->purpose,
                'time_out' => now(),
                'time_out_remark' => $remark,
                'created_by' => $createdBy,
                'created_at' => now()
            ]);
        }

        // Get the latest going out record for success message
        $latestGoingOut = Going_out::where('student_id', $request->student_id)
            ->whereDate('going_out_date', $todayDate)
            ->orderBy('session_number', 'desc')
            ->first();

        $sessionInfo = $isSunday ? '' : ' (Session ' . $latestGoingOut->session_number . ')';
        return redirect()->route('goingOutLogForms.show')->with('success', 'Time-out recorded successfully. You are ' . $remark . '.' . $sessionInfo);
    }

    /**
     * Store a new going-out log (login).
     */
    public function logTimeIn(Request $request)
    {
        // dd($request);
        $request->validate([
            'student_id' => 'required|exists:student_details,student_id',
        ]);

        $student = StudentDetail::where('student_id', $request->student_id)->first();

        if (!$student) {
            Log::warning('Student not found for going out logTimeIn.', [
                'student_id' => $request->student_id,
            ]);
            return redirect()->route('goingOutLogForms.show')->with('error', 'Student not found.');
        }

        $today = Carbon::now()->format('l');

        // Clear any potential model cache and force fresh query
        Schedule::clearBootedModels();

        // First check for individual going-out schedule for this specific student
        $schedule = Schedule::where([
                ['student_id', $student->student_id],
                ['day_of_week', $today],
                ['schedule_type', 'going_out'] // Only look for going-out schedules
            ])->where(function ($query) {
                $query->whereNull('valid_until') // Permanent schedule
                    ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
            })
            ->orderBy('updated_at', 'desc') // Most recently updated first
            ->orderBy('created_at', 'desc')  // Then most recently created
            ->first();

        // If no individual schedule found, fall back to general going-out schedule
        if (!$schedule) {
            $schedule = Schedule::where([
                    ['gender', $student->user->gender],
                    ['day_of_week', $today],
                    ['student_id', null], // General schedules have null student_id
                    ['schedule_type', 'going_out'] // Only look for going-out schedules
                ])->where(function ($query) {
                    $query->whereNull('valid_until') // Permanent schedule
                        ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
                })
                ->orderBy('updated_at', 'desc') // Most recently updated first
                ->orderBy('created_at', 'desc')  // Then most recently created
                ->first();
        }

        if (!$schedule) {
            Log::warning('Student attempted going out login with no schedule', [
                'student_id' => $student->student_id,
                'gender' => $student->user->gender,
                'day' => $today,
            ]);
            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'No schedule set for today! Please contact your educator for assistance.');
        }

        // Going out login: No time restrictions - students can log in anytime after logout

        // Get the current active session (most recent session with time_out but no time_in)
        $goingOut = Going_out::where('student_id', $request->student_id)
            ->whereDate('going_out_date', now()->toDateString())
            ->where('session_status', 'active')
            ->whereNotNull('time_out')
            ->whereNull('time_in')
            ->orderBy('session_number', 'desc')
            ->first();

        if (!$goingOut) {
            Log::warning('No active going out session found for time in.', [
                'student_id' => $request->student_id,
                'going_out_date' => now()->toDateString(),
            ]);
            return redirect()->route('goingOutLogForms.show')->with('error', 'You have not logged out yet or no active session found.');
        }

        // Check if monitor already logged in this student (priority check)
        if ($goingOut->monitor_logged_in) {
            return redirect()->route('goingOutLogForms.show')->with('error', 'Monitor has already logged you in. You cannot log in again.');
        }

        if ($goingOut->time_in) {
            Log::info('Time in already logged for going out session.', [
                'student_id' => $request->student_id,
                'going_out_date' => now()->toDateString(),
                'session_number' => $goingOut->session_number,
            ]);
            return redirect()->route('goingOutLogForms.show')->with('error', 'You have already logged in for this session.');
        }

        if ($goingOut) {
            // Calculate remark based on schedule with fixed timing (no grace period for going out)
            $scheduleEndTime = Carbon::parse($schedule->getRawTimeIn());
            $updatedBy = $student->user->user_fname . ' ' . $student->user->user_lname;
            $currentTime = Carbon::parse(now());

            $remark = 'On Time'; // Default
            if ($schedule) {
                // Fixed timing logic for going out (no grace period)
                // On Time: before or at scheduled end time
                // Late: after scheduled end time
                if ($currentTime->greaterThan($scheduleEndTime)) {
                    $remark = 'Late';
                } else {
                    $remark = 'On Time';
                }
            }

            $goingOut->update([
                'time_in' => now(),
                'time_in_remark' => $remark,
                'session_status' => 'completed',
                'updated_by' => $updatedBy,
                'updated_at' => now()
            ]);
        }

        $isSunday = now()->isSunday();
        $sessionInfo = $isSunday ? '' : ' (Session ' . $goingOut->session_number . ')';
        return redirect()->route('goingOutLogForms.show')->with('success', 'Time-in logged successfully. You are ' . $goingOut->time_in_remark . '.' . $sessionInfo);
    }
}
