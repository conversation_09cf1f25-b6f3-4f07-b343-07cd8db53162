<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('pnph_users')->insert([
            'user_id' => '001',
            'user_fname' => 'admin',
            'user_lname' => 'User',
            'user_mInitial' => null,
            'user_suffix' => null,
            'user_email' => '<EMAIL>',
            'user_role' => 'admin',
            'user_password' => Hash::make('password'),
            'status' => 'active',
            'is_temp_password' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('pnph_users')->insert([
            'user_id' => '002',
            'user_fname' => 'educator',
            'user_lname' => 'User',
            'user_mInitial' => null,
            'user_suffix' => null,
            'user_email' => '<EMAIL>',
            'user_role' => 'educator',
            'user_password' => Hash::make('password'),
            'status' => 'active',
            'is_temp_password' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('pnph_users')->insert([
            'user_id' => '003',
            'user_fname' => 'student',
            'user_lname' => 'User',
            'user_mInitial' => null,
            'user_suffix' => null,
            'user_email' => '<EMAIL>',
            'user_role' => 'student',
            'user_password' => Hash::make('password'),
            'status' => 'active',
            'is_temp_password' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
