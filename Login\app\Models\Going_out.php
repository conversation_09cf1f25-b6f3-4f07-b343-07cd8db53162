<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Going_out extends Model
{
    public $timestamps = false;

    // Optional: if you want to cast is_deleted to boolean
    protected $casts = [
        'is_deleted' => 'boolean',
    ];

    protected $fillable = [
        'student_id',
        'going_out_date',
        'session_number',
        'session_status',
        'destination',
        'purpose',
        'time_out',
        'time_out_remark',
        'time_out_consideration',
        'time_out_reason',
        'time_out_absent_validation',
        'monitor_logged_out',
        'time_in',
        'time_in_remark',
        'educator_consideration',
        'time_in_reason',
        'time_in_absent_validation',
        'monitor_logged_in',
        'created_by',
        'created_at',
        'updated_by',
        'updated_at',
        'is_deleted'
    ];

    public function studentDetail()
    {
        return $this->belongsTo(StudentDetail::class, 'student_id', 'student_id');
    }

    // Get the monitor name who set the time out consideration
    public function getTimeOutMonitorNameAttribute()
    {
        if ($this->time_out_consideration && $this->updated_by) {
            return $this->updated_by;
        }
        return null;
    }

    // Get the monitor name who set the time in consideration
    public function getTimeInMonitorNameAttribute()
    {
        if ($this->educator_consideration && $this->updated_by) {
            return $this->updated_by;
        }
        return null;
    }

    // Format time_out to 12-hour format
    public function getFormattedTimeOutAttribute()
    {
        if (!$this->time_out) {
            return '—';
        }
        return Carbon::createFromFormat('H:i:s', $this->time_out)->format('g:i A');
    }

    // Format time_in to 12-hour format
    public function getFormattedTimeInAttribute()
    {
        if (!$this->time_in) {
            return '—';
        }
        return Carbon::createFromFormat('H:i:s', $this->time_in)->format('g:i A');
    }

    // Format date to more readable format
    public function getFormattedDateAttribute()
    {
        return Carbon::parse($this->going_out_date)->format('F j, Y');
    }

    // Get the next session number for a student on a specific date
    public static function getNextSessionNumber($studentId, $date)
    {
        $lastSession = self::where('student_id', $studentId)
            ->whereDate('going_out_date', $date)
            ->orderBy('session_number', 'desc')
            ->first();

        return $lastSession ? $lastSession->session_number + 1 : 1;
    }

    // Get the current active session for a student on a specific date
    public static function getCurrentActiveSession($studentId, $date)
    {
        return self::where('student_id', $studentId)
            ->whereDate('going_out_date', $date)
            ->where('session_status', 'active')
            ->orderBy('session_number', 'desc')
            ->first();
    }

    // Check if student can start a new session (must complete previous session first)
    public static function canStartNewSession($studentId, $date)
    {
        $activeSession = self::getCurrentActiveSession($studentId, $date);
        return $activeSession === null; // Can start new session if no active session exists
    }

    // Mark session as completed
    public function markAsCompleted()
    {
        $this->update(['session_status' => 'completed']);
    }
}
