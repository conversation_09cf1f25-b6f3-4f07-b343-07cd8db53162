<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;

class P<PERSON>ser extends Authenticatable
{
    use HasApiTokens;

    protected $table = 'pnph_users';

    protected $primaryKey = 'user_id';

    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'user_fname',
        'user_lname',
        'user_mInitial',
        'user_suffix',
        'gender',
        'user_email',
        'user_role',
        'user_password',
        'status',
        'is_temp_password',
        'token'
    ];

    protected $nullable = [
        'user_mInitial',
        'user_suffix',
    ];
}
