<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('grade_submission_proofs', function (Blueprint $table) {
            $table->string('status')->default('pending')->after('file_type');
        });
    }

    public function down()
    {
        Schema::table('grade_submission_proofs', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
}; 