<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add absent validation columns to academics table
        Schema::table('academics', function (Blueprint $table) {
            $table->string('time_in_absent_validation')->nullable()->after('time_in_reason');
            $table->string('time_out_absent_validation')->nullable()->after('time_out_reason');
        });

        // Add absent validation columns to going_outs table
        Schema::table('going_outs', function (Blueprint $table) {
            $table->string('time_in_absent_validation')->nullable()->after('time_in_reason');
            $table->string('time_out_absent_validation')->nullable()->after('time_out_reason');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('academics', function (Blueprint $table) {
            $table->dropColumn(['time_in_absent_validation', 'time_out_absent_validation']);
        });

        Schema::table('going_outs', function (Blueprint $table) {
            $table->dropColumn(['time_in_absent_validation', 'time_out_absent_validation']);
        });
    }
};
