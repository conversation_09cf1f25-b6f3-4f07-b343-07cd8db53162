<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\VisitorController;
use App\Http\Controllers\AcademicLogController;
use App\Http\Controllers\GoingOutLogController;
use App\Models\Visitor;
use Illuminate\Http\Request;
use Laravel\Sanctum\PersonalAccessToken;

Route::get('/', function (Request $request) {
    $token = $request->query('token');
    return view('welcome', compact('token'));
});

Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login'])->name('login.process');

Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

Route::get('/forgot-password', function () {
    return view('forgotPassword');
})->name('forgot-password');

Route::post('/forgot-password/verify', [AuthController::class, 'verifyForgotPassword'])->name('forgot-password.verify');

Route::get('/reset-password', [AuthController::class, 'showResetPasswordForm'])->name('reset-password');
Route::post('/reset-password/update', [AuthController::class, 'resetPassword'])->name('reset-password.update');

Route::get('/main-menu', function (Request $request) {
    $user_role = $request->query('user_role');
    $token = $request->query('token');
    if (!$token) {
        return redirect()->route('login')->with('error', 'Token missing');
    }
    $accessToken = PersonalAccessToken::findToken($token);
    if (!$accessToken) {
        return redirect()->route('login')->with('error', 'Unauthorized');
    }
    return view('landing-page', compact('token', 'user_role'));
})->name('main-menu');

Route::get('/change-password', [AuthController::class, 'showChangePasswordForm'])->name('change-password');
Route::post('/update-password', [AuthController::class, 'updatePassword'])->name('update-password');

//logify edits
Route::get('/visitor/dashboard', function () {
   $visitors = Visitor::orderBy('visitor_date', 'desc')
                   ->orderBy('time_in', 'desc')
                   ->get();
    return view('visitor.dashboard', compact('visitors'));
})->name('visitor.dashboard.show');

Route::post('/visitor/dashboard/{id}', [VisitorController::class, 'logOut'])->name('visitor.logOut');
Route::get('/visitor_page', [VisitorController::class, 'create'])->name('visitor.create');
Route::post('/visitor/add', [VisitorController::class, 'store'])->name('visitor.store');

Route::get('/student/dashboard', function () {
    return view('user-student.dashboard');
})->name('student.dashboard');

Route::get('/student/academic', function () {
    $goingout = false;
    $academic = true;
    return view('user-student.logForms', ['academic' => $academic, 'goingout' => $goingout]);
})->name('academicLogForms.show');

Route::get('/student/goingout', function () {
    $goingout = true;
    $academic = false;
    return view('user-student.logForms', ['academic' => $academic, 'goingout' => $goingout]);
})->name('goingOutLogForms.show');


Route::get('/student/academic/logout/show', [AcademicLogController::class, 'logoutForm'])->name('academic.logout.form');
Route::get('/student/academic/login/show', [AcademicLogController::class, 'loginForm'])->name('academic.login.form');
Route::match(['get', 'post'], '/student/academic/logout', [AcademicLogController::class, 'logTimeOut'])->name('academic.logout');
Route::match(['get', 'post'], '/student/academic/login', [AcademicLogController::class, 'logTimeIn'])->name('academic.login');

Route::get('/student/goingout/logout/show', [GoingOutLogController::class, 'logoutForm'])->name('goingout.logout.form');
Route::get('/student/goingout/login/show', [GoingOutLogController::class, 'loginForm'])->name('goingout.login.form');
Route::post('/student/goingout/logout',     [GoingOutLogController::class, 'logTimeOut'])->name('goingout.logout');
Route::post('/student/goingout/login', [GoingOutLogController::class, 'logTimeIn'])->name('goingout.login');
