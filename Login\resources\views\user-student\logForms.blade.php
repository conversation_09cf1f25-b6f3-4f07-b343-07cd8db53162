<x-studentLayout>
    <div class="container px-4 py-8 mx-auto font-sans sm:px-6 lg:px-8">
        {{-- Header Section with Back Button --}}
        <div class="flex flex-col items-start justify-between mb-8 sm:flex-row">
            <a href="/student/dashboard"
                class="inline-flex items-center justify-center px-3 py-1.5 text-orange-600 transition-all duration-300 bg-orange-200 border border-orange-200 rounded-lg shadow-sm hover:bg-orange-600 hover:text-white hover:border-orange-600">
                <i data-feather="arrow-left" class="w-4 h-4 mr-1"></i>
                <span class="text-sm font-medium">Back to Dashboard</span>
            </a>
        </div>


        {{-- Title Section --}}
        <div class="mb-12 text-center">
            <h1 class="mb-4 text-3xl font-bold text-gray-800 sm:text-4xl">
                @if ($academic && !$goingout)
                    <span class="text-orange-600">Academic Log</span>
                @elseif($goingout && !$academic)
                    <span class="text-orange-600">Going Out Log</span>
                @endif
            </h1>
            <p class="text-lg text-gray-600">Select your logging action below</p>
            <div class="w-24 h-1 mx-auto mt-4 bg-orange-500 rounded-full"></div>
        </div>

        {{-- Logging Options Container --}}
        <div class="max-w-4xl mx-auto">
            <div class="grid max-w-3xl grid-cols-1 gap-4 mx-auto md:grid-cols-2">
                @if ($academic && !$goingout)
                    {{-- Academic Log Out --}}
                    <a href="{{ route('academic.logout.form') }}"
                        class="relative transition-all duration-300 bg-blue-200 border-2 border-blue-500 rounded-lg shadow-md group hover:bg-blue-500">
                        <div class="flex flex-col items-center p-4 text-center">
                            <div
                                class="flex items-center justify-center w-12 h-12 mb-3 transition-colors duration-300 bg-blue-100 rounded-full group-hover:bg-white">
                                <i data-feather="book-open" class="w-6 h-6 text-blue-600 group-hover:text-blue-500"></i>
                            </div>
                            <h3 class="mb-1 text-lg font-semibold text-gray-800 group-hover:text-white">Log Out</h3>
                            <p class="text-sm text-gray-600 group-hover:text-white">Log your departure when leaving for
                                School.</p>
                        </div>
                    </a>

                    {{-- Academic Log In --}}
                    <a href="{{ route('academic.login.form') }}"
                        class="relative transition-all duration-300 bg-orange-200 border-2 border-orange-500 rounded-lg shadow-md group hover:bg-orange-500">
                        <div class="flex flex-col items-center p-4 text-center">
                            <div
                                class="flex items-center justify-center w-12 h-12 mb-3 transition-colors duration-300 bg-orange-100 rounded-full group-hover:bg-white">
                                <i data-feather="log-in"
                                    class="w-6 h-6 text-orange-600 group-hover:text-orange-500"></i>
                            </div>
                            <h3 class="mb-1 text-lg font-semibold text-gray-800 group-hover:text-white">Log In</h3>
                            <p class="text-sm text-gray-600 group-hover:text-white">Log your arrival back at the Center.
                            </p>
                        </div>
                    </a>
                @elseif ($goingout && !$academic)
                    {{-- Going Out Log Out --}}
                    <a href="{{ route('goingout.logout.form') }}"
                        class="relative transition-all duration-300 bg-blue-200 border-2 border-blue-500 rounded-lg shadow-md group hover:bg-blue-500">
                        <div class="flex flex-col items-center p-4 text-center">
                            <div
                                class="flex items-center justify-center w-12 h-12 mb-3 transition-colors duration-300 bg-blue-100 rounded-full group-hover:bg-white">
                                <i data-feather="log-out" class="w-6 h-6 text-blue-600 group-hover:text-blue-500"></i>
                            </div>
                            <h3 class="mb-1 text-lg font-semibold text-gray-800 group-hover:text-white">Log Out</h3>
                            <p class="text-sm text-gray-600 group-hover:text-white">Log your departure when leaving for
                                your intended destination.</p>
                        </div>
                    </a>

                    {{-- Going Out Log In --}}
                    <a href="{{ route('goingout.login.form') }}"
                        class="relative transition-all duration-300 bg-orange-200 border-2 border-orange-500 rounded-lg shadow-md group hover:bg-orange-500">
                        <div class="flex flex-col items-center p-4 text-center">
                            <div
                                class="flex items-center justify-center w-12 h-12 mb-3 transition-colors duration-300 bg-orange-100 rounded-full group-hover:bg-white">
                                <i data-feather="log-in"
                                    class="w-6 h-6 text-orange-600 group-hover:text-orange-500"></i>
                            </div>
                            <h3 class="mb-1 text-lg font-semibold text-gray-800 group-hover:text-white">Log In</h3>
                            <p class="text-sm text-gray-600 group-hover:text-white">Log your return after going out.</p>
                        </div>
                    </a>
                @endif
            </div>
        </div>
        <div class="flex items-center justify-center mt-6 p-15">
            @if (session('success'))
                <div class="flex items-center p-4 mb-6 text-green-800 bg-green-100 rounded-lg justi fy-between">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2"
                            viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M9 12l2 2l4-4"></path>
                            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9s4.03-9 9-9s9 4.03 9 9z"></path>
                        </svg>
                        {{ session('success') }}
                    </div>
                    <button onclick="this.parentElement.remove()" class="text-green-500 hover:text-green-700">
                        ✖
                    </button>
                </div>
            @endif

            @if (session('error'))
                <div class="flex items-center justify-between p-4 mb-6 text-red-800 bg-red-100 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2"
                            viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 9v2m0 4h.01"></path>
                            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9s4.03-9 9-9s9 4.03 9 9z"></path>
                        </svg>
                        {{ session('error') }}
                    </div>
                    <button onclick="this.parentElement.remove()" class="text-red-500 hover:text-red-700">
                        ✖
                    </button>
                </div>
            @endif
        </div>
    </div>
</x-studentLayout>
